from fastapi import FastAPI, Depends, HTTPException
from fastapi.responses import Response, StreamingResponse
from sqlalchemy import create_engine, Column, Integer, String, DateTime, text, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime
import os
from dotenv import load_dotenv
from fastapi.middleware.cors import CORSMiddleware
import re
import requests
from typing import Set
import html
import io
from urllib.parse import quote
import zipfile
import glob
import logging

# 设置日志
logger = logging.getLogger(__name__)

def html_to_text(html_content: str) -> str:
    """
    将HTML内容转换为纯文本
    """
    if not html_content:
        return ""
    # 移除CSS样式规则
    text = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL)
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 解码HTML实体
    text = html.unescape(text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text).strip()
    return text

# 加载数据管道专用环境变量
try:
    load_dotenv("data_pipeline.env")
except:
    # 如果找不到配置文件，使用默认值
    pass

# 创建FastAPI应用
app = FastAPI(title="数据管道API服务", description="ChestnutCMS数据处理服务，提供文章管理和数据同步功能")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8000", "http://127.0.0.1:8000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据管道配置 - 从配置文件读取
DATA_PIPELINE_HOST = os.getenv("DATA_PIPELINE_HOST", "0.0.0.0")
DATA_PIPELINE_PORT = int(os.getenv("DATA_PIPELINE_PORT", "8001"))

# 数据库配置 - 从配置文件读取
DB_USER = os.getenv('DB_USER', 'root')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '3306')
DB_NAME = os.getenv('DB_NAME', 'chestnut_cms')

DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# 网站配置 - 从配置文件读取
SITE_BASE_URL = os.getenv("SITE_BASE_URL", "https://gzmdrw.cn")

# RAG服务配置 - 从配置文件读取
RAG_SERVICE_URL = os.getenv("RAG_SERVICE_URL", "http://localhost:8000")

# 数据库连接状态
db_connected = False
engine = None
SessionLocal = None
Base = None

# 尝试创建数据库引擎
try:
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    Base = declarative_base()
    
    # 测试连接
    with engine.connect() as conn:
        conn.execute(text("SELECT 1"))
    db_connected = True
    # 连接成功时不输出任何信息，避免重复输出
except Exception as e:
    print(f"⚠️  数据库连接失败: {e}")
    print("   数据管道API服务将以有限功能模式运行")
    db_connected = False

# 数据库依赖
def get_db():
    if not db_connected:
        raise HTTPException(status_code=503, detail="数据库连接不可用")
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 栏目模型
class Catalog(Base):
    __tablename__ = "cms_catalog"
    
    catalog_id = Column(Integer, primary_key=True)
    path = Column(String(200))

# 文章模型
class Content(Base):
    __tablename__ = "cms_content"
    
    content_id = Column(Integer, primary_key=True)
    content_type = Column(String(50))
    title = Column(String(200))
    publish_date = Column(DateTime)
    update_time = Column(DateTime)
    redirect_url = Column(String(500))
    catalog_id = Column(Integer, ForeignKey('cms_catalog.catalog_id'))

class ArticleDetail(Base):
    __tablename__ = "cms_article_detail"
    
    content_id = Column(Integer, primary_key=True)
    content_html = Column(Text)

# Pydantic模型
class ArticleBase(BaseModel):
    title: str
    content_type: str = "article"
    publish_date: Optional[datetime] = None
    update_time: Optional[datetime] = None

class ArticleDetailBase(BaseModel):
    content_html: str

class ArticleResponse(ArticleBase):
    content_id: int
    redirect_url: Optional[str] = None
    content_url: Optional[str] = None
    content_txt: Optional[str] = None
    action: Optional[str] = None  # 新增字段，用于标记操作类型
    filename: Optional[str] = None  # 处理后的文件名
    chunks_count: Optional[int] = None  # 文档块数量
    file_size: Optional[int] = None  # 文件大小
    file_path: Optional[str] = None  # 文件路径

    class Config:
        from_attributes = True

# API路由
@app.get("/")
def read_root():
    return {
        "message": "Welcome to ChestnutCMS API",
        "status": "running",
        "database_connected": db_connected,
        "services": {
            "main_api": "http://localhost:8000",
            "pipeline_api": f"http://localhost:{DATA_PIPELINE_PORT}"
        }
    }

@app.get("/status")
def get_status():
    """获取服务状态"""
    return {
        "service": "数据管道API服务",
        "status": "running",
        "database_connected": db_connected,
        "database_config": {
            "host": DB_HOST,
            "port": DB_PORT,
            "database": DB_NAME,
            "user": DB_USER
        },
        "rag_service_url": RAG_SERVICE_URL,
        "site_base_url": SITE_BASE_URL
    }

@app.get("/debug/rag-connection")
async def test_rag_connection():
    """测试与RAG服务的连接"""
    try:
        print(f"🔍 测试RAG服务连接: {RAG_SERVICE_URL}")
        
        # 测试健康检查
        health_response = requests.get(f"{RAG_SERVICE_URL}/", timeout=5)
        health_status = "✅ 正常" if health_response.status_code == 200 else f"❌ 异常 ({health_response.status_code})"
        
        # 测试状态接口
        status_response = requests.get(f"{RAG_SERVICE_URL}/api/status", timeout=5)
        status_data = status_response.json() if status_response.status_code == 200 else None
        
        # 测试文档接口
        documents_response = requests.get(f"{RAG_SERVICE_URL}/api/documents", timeout=5)
        documents_data = documents_response.json() if documents_response.status_code == 200 else None
        
        return {
            "rag_service_url": RAG_SERVICE_URL,
            "connection_test": {
                "health_check": {
                    "status": health_status,
                    "response_code": health_response.status_code,
                    "response": health_response.json() if health_response.status_code == 200 else None
                },
                "status_api": {
                    "status": "✅ 正常" if status_response.status_code == 200 else f"❌ 异常 ({status_response.status_code})",
                    "response_code": status_response.status_code,
                    "data": status_data
                },
                "documents_api": {
                    "status": "✅ 正常" if documents_response.status_code == 200 else f"❌ 异常 ({documents_response.status_code})",
                    "response_code": documents_response.status_code,
                    "documents_count": len(documents_data.get('documents', [])) if documents_data else 0,
                    "data": documents_data
                }
            },
            "configuration": {
                "data_pipeline_host": DATA_PIPELINE_HOST,
                "data_pipeline_port": DATA_PIPELINE_PORT,
                "site_base_url": SITE_BASE_URL,
                "database_url": f"mysql+pymysql://{DB_USER}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}"
            }
        }
        
    except Exception as e:
        return {
            "rag_service_url": RAG_SERVICE_URL,
            "error": str(e),
            "connection_test": {
                "health_check": {"status": "❌ 连接失败", "error": str(e)},
                "status_api": {"status": "❌ 连接失败", "error": str(e)},
                "documents_api": {"status": "❌ 连接失败", "error": str(e)}
            }
        }

def generate_content_url(content_id: int, catalog_id: int, db: Session) -> str:
    """
    根据catalog_id获取path并生成文章URL
    """
    catalog = db.query(Catalog).filter(Catalog.catalog_id == catalog_id).first()
    if catalog and catalog.path:
        return f"{SITE_BASE_URL}/{catalog.path}{content_id}.shtml"
    return SITE_BASE_URL  # 如果找不到catalog或path，返回默认URL

def get_rag_content() -> list:
    """
    从RAG服务获取文档列表，兼容 {success, documents: [...]} 结构
    """
    try:
        print(f"🔍 正在从RAG服务获取文档列表: {RAG_SERVICE_URL}/api/documents")
        response = requests.get(f"{RAG_SERVICE_URL}/api/documents", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and data.get('success') and 'documents' in data:
                print(f"✅ 成功获取文档列表，响应状态: {data.get('success', False)}")
                print(f"📄 找到 {len(data['documents'])} 个文档")
                return data['documents']
            elif isinstance(data, list):
                print(f"📄 直接返回 {len(data)} 个文档")
                return data
            else:
                print(f"⚠️ 意外的响应格式: {type(data)}")
                return []
        else:
            print(f"❌ RAG服务响应异常: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取RAG文档失败: {e}")
        return []

@app.get("/articles/to-add", response_model=List[ArticleResponse])
async def get_articles_to_add(
    db: Session = Depends(get_db)
):
    """
    获取需要添加到文档管理的文章列表
    - 新增：CMS中存在，但RAG中不存在的文章
    - 更新：CMS和RAG中都存在，但CMS中版本更新的文章
    """
    # 获取所有文章（不再限制正文内容）
    local_articles = db.query(Content).filter(Content.content_type == "article").all()
    
    # 获取RAG中的文档内容列表
    documents = get_rag_content()
    
    # 创建RAG文档字典以便快速查找 {filename: document_meta}
    documents_dict = {doc["filename"]: doc for doc in documents if doc.get("filename")}
    document_filenames = set(documents_dict.keys())

    # 创建本地CMS文章字典以便快速查找 {filename: article_object}
    local_articles_dict = {
        process_filename(article.title): article 
        for article in local_articles if article.title
    }
    local_filenames = set(local_articles_dict.keys())
    
    # 1. 找出需要新增的文章 (CMS有，RAG无)
    to_add_filenames = local_filenames - document_filenames
    
    # 2. 找出需要更新的文章 (CMS和RAG共有，但CMS版本新)
    to_update_filenames = set()
    common_filenames = local_filenames & document_filenames
    for filename in common_filenames:
        local_article = local_articles_dict.get(filename)
        rag_document = documents_dict.get(filename)

        if local_article and rag_document:
            local_time = local_article.update_time
            doc_time_str = rag_document.get("file_modified")
            
            if local_time and doc_time_str:
                try:
                    # 将CMS时间转换为Unix时间戳进行比较（更精确，避免时区问题）
                    local_timestamp = local_time.timestamp()
                    doc_timestamp = float(doc_time_str)

                    # 如果本地文章的更新时间 > RAG文档的修改时间，则需要更新
                    if local_timestamp > doc_timestamp:
                        to_update_filenames.add(filename)
                        logger.info(f"文章需要更新: {filename}")
                        logger.info(f"  CMS更新时间: {local_time.isoformat()} (时间戳: {local_timestamp})")
                        logger.info(f"  RAG写入时间: {datetime.fromtimestamp(doc_timestamp).isoformat()} (时间戳: {doc_timestamp})")
                    else:
                        logger.debug(f"文章无需更新: {filename}, CMS时间({local_timestamp}) <= RAG时间({doc_timestamp})")

                except (ValueError, TypeError) as e:
                    logger.warning(f"时间比较失败 {filename}: {e}")
                    # 发生错误时，使用原来的DateTime比较作为备选方案
                    doc_time = timestamp_to_datetime(doc_time_str)
                    if doc_time and local_time > doc_time:
                        to_update_filenames.add(filename)
                        logger.info(f"使用备选方案更新文章: {filename}")

    # 合并新增和更新的列表
    final_filenames_to_process = to_add_filenames | to_update_filenames
    
    # 获取需要添加和更新的文章详情
    result = []
    for article in local_articles:
        processed_filename = process_filename(article.title)
        if processed_filename in final_filenames_to_process:
            # 处理URL
            redirect_url = article.redirect_url
            content_url = None
            
            if not redirect_url and article.catalog_id:
                content_url = generate_content_url(article.content_id, article.catalog_id, db)
            
            # 判断操作类型
            is_update = processed_filename in to_update_filenames
            action = "update" if is_update else "add"
            
            article_dict = {
                "content_id": article.content_id,
                "title": article.title,
                "publish_date": article.publish_date,
                "filename": processed_filename,
                "content_type": article.content_type,
                "update_time": article.update_time,
                "redirect_url": redirect_url,
                "content_url": content_url,
                "action": action,  # 明确标记操作类型
                "file_path": None
            }
            result.append(article_dict)
    
    print(f"📊 同步统计: 新增 {len(to_add_filenames)} 个, 更新 {len(to_update_filenames)} 个")
    return result

@app.get("/articles/to-delete", response_model=List[ArticleResponse])
async def get_articles_to_delete(
    db: Session = Depends(get_db)
):
    """
    获取需要从文档管理删除的文章列表
    基于文件名对比官网数据库和文档管理服务
    """
    # 获取本地文章列表
    local_articles = db.query(Content).filter(
        Content.content_type == "article"
    ).all()
    
    # 获取文档内容列表
    documents = get_rag_content()
    
    # 获取文件名集合
    local_filenames = {process_filename(article.title) for article in local_articles if article.title}
    document_filenames = get_document_filenames(documents)
    
    # 找出需要删除的文章（文档管理中有但本地没有）
    to_delete_filenames = document_filenames - local_filenames
    
    # 从文档内容中筛选出需要删除的文章
    result = []
    for document in documents:
        if document.get("filename") in to_delete_filenames:
            # 构建与to-add接口相同的数据结构
            article_dict = {
                "content_id": 0,  # 文档管理中的文档没有content_id
                "title": document.get("filename", ""),  # 使用文件名作为title
                "content_type": "article",
                "publish_date": None,
                "update_time": document.get("file_modified"),  # 直接使用文档管理中的时间戳
                "redirect_url": None,
                "content_url": None,
                "content_html": None,
                "content_txt": None,
                "action": "delete",  # 标记为需要删除
                "filename": document.get("filename"),
                "chunks_count": document.get("chunks_count"),
                "file_size": document.get("file_size"),
                "file_path": document.get("file_path")
            }
            result.append(article_dict)
    
    return result

@app.get("/articles/{article_id}/txt")
def get_article_txt(article_id: int, db: Session = Depends(get_db)):
    try:
        article = db.query(Content).filter(Content.content_id == article_id).first()
        if article is None:
            raise HTTPException(status_code=404, detail="文章不存在 (content_id in cms_content not found)")
        
        detail = db.query(ArticleDetail).filter(
            ArticleDetail.content_id == article_id
        ).first()
        
        content_txt = None
        if detail and detail.content_html:
            content_txt = html_to_text(detail.content_html)
        
        # 优先返回正文内容
        if content_txt:
            file_content = content_txt
        # 如果没有正文内容，返回 redirect_url
        elif article.redirect_url:
            file_content = article.redirect_url
        # 如果连 redirect_url 也没有，抛出 404
        else:
            raise HTTPException(status_code=404, detail="文章无正文且无跳转链接 (无可用内容)")
        
        filename = f"{article.title}.txt"
        filename_utf8 = quote(filename)
        return StreamingResponse(
            io.BytesIO(file_content.encode("utf-8")),
            media_type="text/plain; charset=utf-8",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{filename_utf8}"
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in get_article_txt: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/articles/")
def download_all_articles(db: Session = Depends(get_db)):
    articles = db.query(Content).filter(Content.content_type == "article").order_by(Content.update_time.desc()).all()
    if not articles:
        raise HTTPException(status_code=404, detail="No articles found")
    
    zip_buffer = io.BytesIO()
    title_count = {}
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for article in articles:
            detail = db.query(ArticleDetail).filter(
                ArticleDetail.content_id == article.content_id
            ).first()
            if not detail or not detail.content_html:
                continue
            content_txt = html_to_text(detail.content_html)
            if not content_txt:
                continue
            # 处理文件名：只用标题，重复标题自动加(1)、(2)...
            base_title = re.sub(r'[\\/:*?"<>|]', '_', article.title)
            if base_title not in title_count:
                filename = f"{base_title}.txt"
                title_count[base_title] = 1
            else:
                filename = f"{base_title}({title_count[base_title]}).txt"
                title_count[base_title] += 1
            # 只写入 content_txt
            file_content = content_txt
            zip_file.writestr(filename, file_content)
    zip_buffer.seek(0)
    zip_filename = "所有文章txt.zip"
    zip_filename_ascii = "all_articles.zip"
    zip_filename_utf8 = quote(zip_filename)
    headers = {
        "Content-Disposition": f"attachment; filename={zip_filename_ascii}; filename*=UTF-8''{zip_filename_utf8}"
    }
    return StreamingResponse(
        zip_buffer,
        media_type="application/zip",
        headers=headers
    )

@app.get("/articles/summary")
def get_articles_summary(db: Session = Depends(get_db)):
    articles = db.query(Content).all()
    result = []
    for article in articles:
        # 生成正式链接
        content_url = None
        if not article.redirect_url and article.catalog_id:
            content_url = generate_content_url(article.content_id, article.catalog_id, db)
        result.append({
            "content_id": article.content_id,
            "content_type": article.content_type,
            "title": process_filename(article.title),  # 使用处理后的文件名作为title
            "redirect_url": article.redirect_url,
            "content_url": content_url,
            "update_time": datetime_to_timestamp(article.update_time)  # 转换为时间戳格式
        })
    return result

def get_document_filenames(documents: List[Dict]) -> Set[str]:
    """
    从文档列表中提取文件名集合
    """
    return {doc["filename"] for doc in documents if doc.get("filename")}

def process_filename(title: str) -> str:
    """
    处理文件名，将文章标题转换为标准文件名格式
    移除特殊字符，保留中文和基本标点
    """
    if not title:
        return ""
    # 移除不适合做文件名的字符
    filename = re.sub(r'[\\/:*?"<>|]', '_', title)
    # 移除多余的下划线
    filename = re.sub(r'_+', '_', filename)
    # 移除首尾的下划线
    filename = filename.strip('_')
    # 添加.txt后缀
    return f"{filename}.txt"

def datetime_to_timestamp(dt: datetime) -> str:
    """
    将datetime对象转换为Unix时间戳字符串格式
    返回格式：1750332554.011752
    """
    if not dt:
        return ""
    # 转换为Unix时间戳（秒）
    timestamp = dt.timestamp()
    # 返回字符串格式，保留6位小数
    return f"{timestamp:.6f}"

def timestamp_to_datetime(timestamp_str: str) -> datetime:
    """
    将Unix时间戳字符串转换为datetime对象
    """
    if not timestamp_str:
        return None
    try:
        timestamp = float(timestamp_str)
        return datetime.fromtimestamp(timestamp)
    except (ValueError, TypeError):
        return None

@app.get("/debug/sync-status")
def debug_sync_status(db: Session = Depends(get_db)):
    """
    返回CMS、RAG文档数量、文件名列表，以及差异，便于同步调试。
    """
    # 1. CMS所有文章（标准化文件名）
    cms_articles = db.query(Content).filter(Content.content_type == "article").all()
    cms_titles = [process_filename(article.title) for article in cms_articles if article.title]
    cms_titles_set = set(cms_titles)

    # 2. RAG服务所有文档
    rag_documents = get_rag_content()
    rag_titles = [doc["filename"] for doc in rag_documents if doc.get("filename")]
    rag_titles_set = set(rag_titles)

    # 差异分析
    cms_not_in_rag = sorted(list(cms_titles_set - rag_titles_set))

    return {
        "cms_count": len(cms_titles),
        "rag_count": len(rag_titles),
        "cms_titles": cms_titles,
        "rag_titles": rag_titles,
        "cms_titles_not_in_rag": cms_not_in_rag
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=DATA_PIPELINE_HOST, port=DATA_PIPELINE_PORT) 