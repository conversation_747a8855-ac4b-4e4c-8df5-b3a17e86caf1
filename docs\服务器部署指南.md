# RAG聊天应用 - 阿里云服务器部署指南

## 📋 目录
1. [服务器环境准备](#1-服务器环境准备)
2. [项目文件上传](#2-项目文件上传)
3. [Python环境配置](#3-python环境配置)
4. [数据库配置](#4-数据库配置)
5. [环境变量配置](#5-环境变量配置)
6. [端口配置](#6-端口配置)
7. [Nginx配置](#7-nginx配置)
8. [进程管理配置](#8-进程管理配置)
9. [SSL证书配置](#9-ssl证书配置)
10. [启动和测试](#10-启动和测试)
11. [故障排除](#11-故障排除)

## 1. 服务器环境准备

### 1.1 系统要求
- **操作系统**: CentOS 7/8, Ubuntu 18.04/20.04/22.04, 或 Debian 9/10/11
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 20GB，推荐 50GB+
- **CPU**: 最少 2核，推荐 4核+

### 1.2 连接服务器
```bash
# 使用SSH连接服务器
ssh root@your_server_ip

# 或使用密钥连接
ssh -i your_key.pem root@your_server_ip
```

### 1.3 更新系统包
```bash
# CentOS/RHEL
sudo yum update -y
sudo yum install -y wget curl git vim

# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y
sudo apt install -y wget curl git vim
```

### 1.4 安装基础软件
```bash
# CentOS/RHEL
sudo yum install -y epel-release
sudo yum install -y python3 python3-pip python3-venv nginx mysql-server

# Ubuntu/Debian
sudo apt install -y python3 python3-pip python3-venv nginx mysql-server
```

## 2. 项目文件上传

### 2.1 创建项目目录
```bash
# 创建项目根目录
sudo mkdir -p /opt/ragapp
sudo chown $USER:$USER /opt/ragapp
cd /opt/ragapp
```

### 2.2 上传项目文件
有几种方式上传项目文件：

**方式1: 使用SCP上传**
```bash
# 在本地执行，上传整个项目
scp -r /path/to/your/fast-gzmdrw-chat root@your_server_ip:/opt/ragapp/

# 或者打包后上传
tar -czf ragapp.tar.gz fast-gzmdrw-chat/
scp ragapp.tar.gz root@your_server_ip:/opt/ragapp/
```

**方式2: 使用Git克隆**
```bash
# 如果项目在Git仓库中
cd /opt/ragapp
git clone https://github.com/your-username/your-repo.git .
```

**方式3: 使用SFTP工具**
- 使用FileZilla、WinSCP等工具上传

### 2.3 设置文件权限
```bash
cd /opt/ragapp
sudo chown -R $USER:$USER .
chmod -R 755 .
```

## 3. Python环境配置

### 3.1 创建虚拟环境
```bash
cd /opt/ragapp
python3 -m venv venv
source venv/bin/activate
```

### 3.2 升级pip
```bash
pip install --upgrade pip
```

### 3.3 安装项目依赖
```bash
pip install -r requirements.txt
```

### 3.4 验证安装
```bash
python -c "import fastapi, uvicorn, llama_index; print('Dependencies installed successfully')"
```

## 4. 数据库配置

### 4.1 启动MySQL服务
```bash
# CentOS/RHEL
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu/Debian
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 4.2 安全配置MySQL
```bash
sudo mysql_secure_installation
```

### 4.3 创建数据库和用户
```bash
# 登录MySQL
sudo mysql -u root -p

# 在MySQL中执行
CREATE DATABASE ragapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ragapp'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON ragapp.* TO 'ragapp'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 5. 环境变量配置

### 5.1 创建主环境配置文件
```bash
cd /opt/ragapp
cp .env.template .env
```

### 5.2 编辑环境变量
```bash
vim .env
```

**必须配置的关键项目：**
```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai-proxy.org/v1

# 数据库配置
DATABASE_URL=mysql+pymysql://ragapp:your_strong_password@localhost:3306/ragapp

# 服务端口配置（避免冲突）
RAG_SERVICE_PORT=9000
DATA_PIPELINE_PORT=9001

# 文件存储路径
DATA_DIR=/opt/ragapp/data
STORAGE_DIR=/opt/ragapp/storage

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=your_domain.com,your_server_ip
```

### 5.3 创建必要目录
```bash
mkdir -p /opt/ragapp/logs
mkdir -p /opt/ragapp/data
mkdir -p /opt/ragapp/storage
```

## 6. 端口配置

### 6.1 检查端口占用
```bash
# 检查常用端口是否被占用
netstat -tlnp | grep -E ':(8000|8001|9000|9001)'
```

### 6.2 修改应用端口配置
如果默认端口被占用，修改 `.env` 文件中的端口配置：
```env
RAG_SERVICE_PORT=9000
DATA_PIPELINE_PORT=9001
```

### 6.3 开放防火墙端口
```bash
# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian (ufw)
sudo ufw allow 9000/tcp
sudo ufw allow 9001/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 6.4 阿里云安全组配置
在阿里云控制台中：
1. 进入ECS实例管理
2. 点击"安全组"
3. 添加安全组规则：
   - 端口范围：9000/9000，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：9001/9001，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：80/80，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：443/443，协议：TCP，授权对象：0.0.0.0/0

## 7. Nginx配置

### 7.1 创建Nginx配置文件
```bash
sudo vim /etc/nginx/sites-available/ragapp
```

### 7.2 Nginx配置内容
```nginx
# RAG聊天应用 Nginx配置
server {
    listen 80;
    server_name your_domain.com your_server_ip;

    # 日志配置
    access_log /var/log/nginx/ragapp_access.log;
    error_log /var/log/nginx/ragapp_error.log;

    # 客户端上传大小限制
    client_max_body_size 100M;

    # 静态文件服务
    location /static/ {
        alias /opt/ragapp/frontend/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # 前端页面
    location / {
        root /opt/ragapp/frontend/templates;
        index index.html;
        try_files $uri $uri/ /index.html;

        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }

    # RAG服务API代理
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 数据管道API代理
    location /data-api/ {
        rewrite ^/data-api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 7.3 启用配置
```bash
# 创建软链接启用站点
sudo ln -s /etc/nginx/sites-available/ragapp /etc/nginx/sites-enabled/

# 删除默认配置（可选）
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 8. 进程管理配置

### 8.1 安装Supervisor
```bash
# CentOS/RHEL
sudo yum install -y supervisor

# Ubuntu/Debian
sudo apt install -y supervisor
```

### 8.2 创建RAG服务配置
```bash
sudo vim /etc/supervisor/conf.d/ragapp-main.conf
```

配置内容：
```ini
[program:ragapp-main]
command=/opt/ragapp/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-main.log
environment=PATH="/opt/ragapp/venv/bin"
```

### 8.3 创建数据管道服务配置
```bash
sudo vim /etc/supervisor/conf.d/ragapp-pipeline.conf
```

配置内容：
```ini
[program:ragapp-pipeline]
command=/opt/ragapp/venv/bin/python data_pipeline.py
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-pipeline.log
environment=PATH="/opt/ragapp/venv/bin"
```

### 8.4 启动Supervisor服务
```bash
# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start ragapp-main
sudo supervisorctl start ragapp-pipeline

# 查看状态
sudo supervisorctl status

# 设置开机自启
sudo systemctl enable supervisor
```

## 9. SSL证书配置（可选但推荐）

### 9.1 安装Certbot
```bash
# CentOS/RHEL
sudo yum install -y certbot python3-certbot-nginx

# Ubuntu/Debian
sudo apt install -y certbot python3-certbot-nginx
```

### 9.2 获取SSL证书
```bash
sudo certbot --nginx -d your_domain.com
```

### 9.3 自动续期
```bash
# 添加到crontab
sudo crontab -e

# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 10. 启动和测试

### 10.1 检查服务状态
```bash
# 检查Supervisor管理的进程
sudo supervisorctl status

# 检查端口监听
netstat -tlnp | grep -E ':(9000|9001)'

# 检查Nginx状态
sudo systemctl status nginx
```

### 10.2 测试API接口
```bash
# 测试RAG服务
curl http://your_server_ip/api/status

# 测试数据管道服务
curl http://your_server_ip/data-api/articles/summary
```

### 10.3 访问前端页面
在浏览器中访问：
- HTTP: `http://your_domain.com` 或 `http://your_server_ip`
- HTTPS: `https://your_domain.com`（如果配置了SSL）

## 11. 故障排除

### 11.1 常见问题

**问题1: 端口被占用**
```bash
# 查找占用端口的进程
sudo lsof -i :9000
sudo lsof -i :9001

# 杀死进程
sudo kill -9 PID
```

**问题2: 权限问题**
```bash
# 修复文件权限
sudo chown -R www-data:www-data /opt/ragapp
sudo chmod -R 755 /opt/ragapp
```

**问题3: Python依赖问题**
```bash
# 重新安装依赖
cd /opt/ragapp
source venv/bin/activate
pip install --upgrade -r requirements.txt
```

### 11.2 日志查看
```bash
# 查看应用日志
tail -f /opt/ragapp/logs/ragapp-main.log
tail -f /opt/ragapp/logs/ragapp-pipeline.log

# 查看Nginx日志
tail -f /var/log/nginx/ragapp_access.log
tail -f /var/log/nginx/ragapp_error.log

# 查看系统日志
journalctl -u nginx -f
journalctl -u supervisor -f
```

### 11.3 重启服务
```bash
# 重启应用服务
sudo supervisorctl restart ragapp-main
sudo supervisorctl restart ragapp-pipeline

# 重启Nginx
sudo systemctl restart nginx

# 重启所有相关服务
sudo systemctl restart supervisor nginx
```

### 11.4 性能优化建议

**1. 系统优化**
```bash
# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

**2. Nginx优化**
```bash
# 编辑Nginx主配置
sudo vim /etc/nginx/nginx.conf

# 添加以下配置
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

**3. 数据库优化**
```bash
# 编辑MySQL配置
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf

# 添加以下配置
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
```

---

## 📞 技术支持

### 常用管理命令
```bash
# 查看所有服务状态
sudo supervisorctl status
sudo systemctl status nginx mysql

# 重启所有服务
sudo supervisorctl restart all
sudo systemctl restart nginx

# 查看实时日志
tail -f /opt/ragapp/logs/*.log

# 备份数据
mysqldump -u ragapp -p ragapp > backup.sql
tar -czf data_backup.tar.gz /opt/ragapp/data/
```

### 监控脚本
创建简单的监控脚本：
```bash
#!/bin/bash
# 保存为 /opt/ragapp/monitor.sh

echo "=== RAG应用状态监控 ==="
echo "时间: $(date)"
echo

echo "=== 服务状态 ==="
sudo supervisorctl status

echo "=== 端口监听 ==="
netstat -tlnp | grep -E ':(9000|9001|80|443)'

echo "=== 系统资源 ==="
free -h
df -h /opt/ragapp

echo "=== 最近错误 ==="
tail -n 5 /opt/ragapp/logs/ragapp-main.log | grep -i error || echo "无错误"
```

如果在部署过程中遇到问题，请检查：
1. 服务器系统日志
2. 应用程序日志
3. Nginx错误日志
4. 防火墙和安全组配置

部署完成后，您的RAG聊天应用将通过以下方式访问：
- **前端界面**: `http://your_domain.com`
- **RAG API**: `http://your_domain.com/api/`
- **数据管道API**: `http://your_domain.com/data-api/`
- **API文档**: `http://your_domain.com/api/docs`

## 2. 项目文件上传

### 2.1 创建项目目录
```bash
# 创建项目根目录
sudo mkdir -p /opt/ragapp
sudo chown $USER:$USER /opt/ragapp
cd /opt/ragapp
```

### 2.2 上传项目文件
有几种方式上传项目文件：

**方式1: 使用SCP上传**
```bash
# 在本地执行，上传整个项目
scp -r /path/to/your/fast-gzmdrw-chat root@your_server_ip:/opt/ragapp/

# 或者打包后上传
tar -czf ragapp.tar.gz fast-gzmdrw-chat/
scp ragapp.tar.gz root@your_server_ip:/opt/ragapp/
```

**方式2: 使用Git克隆**
```bash
# 如果项目在Git仓库中
cd /opt/ragapp
git clone https://github.com/your-username/your-repo.git .
```

**方式3: 使用SFTP工具**
- 使用FileZilla、WinSCP等工具上传

### 2.3 设置文件权限
```bash
cd /opt/ragapp
sudo chown -R $USER:$USER .
chmod -R 755 .
```

## 3. Python环境配置

### 3.1 创建虚拟环境
```bash
cd /opt/ragapp
python3 -m venv venv
source venv/bin/activate
```

### 3.2 升级pip
```bash
pip install --upgrade pip
```

### 3.3 安装项目依赖
```bash
pip install -r requirements.txt
```

### 3.4 验证安装
```bash
python -c "import fastapi, uvicorn, llama_index; print('Dependencies installed successfully')"
```

## 4. 数据库配置

### 4.1 启动MySQL服务
```bash
# CentOS/RHEL
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu/Debian
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 4.2 安全配置MySQL
```bash
sudo mysql_secure_installation
```

### 4.3 创建数据库和用户
```bash
# 登录MySQL
sudo mysql -u root -p

# 在MySQL中执行
CREATE DATABASE ragapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ragapp'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON ragapp.* TO 'ragapp'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 5. 环境变量配置

### 5.1 创建主环境配置文件
```bash
cd /opt/ragapp
cp backend/app/data_pipeline.env .env
```

### 5.2 编辑环境变量
```bash
vim .env
```

添加以下配置：
```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai-proxy.org/v1

# 数据库配置
DATABASE_URL=mysql+pymysql://ragapp:your_strong_password@localhost:3306/ragapp

# 服务端口配置（避免冲突）
RAG_SERVICE_PORT=9000
DATA_PIPELINE_PORT=9001

# 文件存储路径
DATA_DIR=/opt/ragapp/data
STORAGE_DIR=/opt/ragapp/storage

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/opt/ragapp/logs/app.log

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=your_domain.com,your_server_ip
```

### 5.3 创建日志目录
```bash
mkdir -p /opt/ragapp/logs
mkdir -p /opt/ragapp/data
mkdir -p /opt/ragapp/storage
```

## 6. 端口配置

### 6.1 检查端口占用
```bash
# 检查常用端口是否被占用
netstat -tlnp | grep -E ':(8000|8001|9000|9001)'
```

### 6.2 修改应用端口配置
编辑后端配置文件：

**修改 backend/config/settings.py**
```python
# 如果8000端口被占用，改为9000
RAG_SERVICE_PORT = int(os.getenv("RAG_SERVICE_PORT", "9000"))
DATA_PIPELINE_PORT = int(os.getenv("DATA_PIPELINE_PORT", "9001"))
```

### 6.3 开放防火墙端口
```bash
# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian (ufw)
sudo ufw allow 9000/tcp
sudo ufw allow 9001/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 6.4 阿里云安全组配置
在阿里云控制台中：
1. 进入ECS实例管理
2. 点击"安全组"
3. 添加安全组规则：
   - 端口范围：9000/9000，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：9001/9001，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：80/80，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：443/443，协议：TCP，授权对象：0.0.0.0/0

## 7. Nginx配置

### 7.1 创建Nginx配置文件
```bash
sudo vim /etc/nginx/sites-available/ragapp
```

### 7.2 Nginx配置内容
```nginx
# RAG聊天应用 Nginx配置
server {
    listen 80;
    server_name your_domain.com your_server_ip;
    
    # 日志配置
    access_log /var/log/nginx/ragapp_access.log;
    error_log /var/log/nginx/ragapp_error.log;
    
    # 客户端上传大小限制
    client_max_body_size 100M;
    
    # 静态文件服务
    location /static/ {
        alias /opt/ragapp/frontend/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 前端页面
    location / {
        root /opt/ragapp/frontend/templates;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
    
    # RAG服务API代理
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 数据管道API代理
    location /data-api/ {
        rewrite ^/data-api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 7.3 启用配置
```bash
# 创建软链接启用站点
sudo ln -s /etc/nginx/sites-available/ragapp /etc/nginx/sites-enabled/

# 删除默认配置（可选）
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 8. 进程管理配置

### 8.1 安装Supervisor
```bash
# CentOS/RHEL
sudo yum install -y supervisor

# Ubuntu/Debian
sudo apt install -y supervisor
```

### 8.2 创建RAG服务配置
```bash
sudo vim /etc/supervisor/conf.d/ragapp-main.conf
```

配置内容：
```ini
[program:ragapp-main]
command=/opt/ragapp/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-main.log
environment=PATH="/opt/ragapp/venv/bin"
```

### 8.3 创建数据管道服务配置
```bash
sudo vim /etc/supervisor/conf.d/ragapp-pipeline.conf
```

配置内容：
```ini
[program:ragapp-pipeline]
command=/opt/ragapp/venv/bin/python data_pipeline.py
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-pipeline.log
environment=PATH="/opt/ragapp/venv/bin"
```

### 8.4 启动Supervisor服务
```bash
# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start ragapp-main
sudo supervisorctl start ragapp-pipeline

# 查看状态
sudo supervisorctl status

# 设置开机自启
sudo systemctl enable supervisor
```

## 9. SSL证书配置（可选但推荐）

### 9.1 安装Certbot
```bash
# CentOS/RHEL
sudo yum install -y certbot python3-certbot-nginx

# Ubuntu/Debian
sudo apt install -y certbot python3-certbot-nginx
```

### 9.2 获取SSL证书
```bash
sudo certbot --nginx -d your_domain.com
```

### 9.3 自动续期
```bash
# 添加到crontab
sudo crontab -e

# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 10. 启动和测试

### 10.1 检查服务状态
```bash
# 检查Supervisor管理的进程
sudo supervisorctl status

# 检查端口监听
netstat -tlnp | grep -E ':(9000|9001)'

# 检查Nginx状态
sudo systemctl status nginx
```

### 10.2 测试API接口
```bash
# 测试RAG服务
curl http://your_server_ip/api/status

# 测试数据管道服务
curl http://your_server_ip/data-api/articles/summary
```

### 10.3 访问前端页面
在浏览器中访问：
- HTTP: `http://your_domain.com` 或 `http://your_server_ip`
- HTTPS: `https://your_domain.com`（如果配置了SSL）

## 11. 故障排除

### 11.1 常见问题

**问题1: 端口被占用**
```bash
# 查找占用端口的进程
sudo lsof -i :9000
sudo lsof -i :9001

# 杀死进程
sudo kill -9 PID
```

**问题2: 权限问题**
```bash
# 修复文件权限
sudo chown -R www-data:www-data /opt/ragapp
sudo chmod -R 755 /opt/ragapp
```

**问题3: Python依赖问题**
```bash
# 重新安装依赖
cd /opt/ragapp
source venv/bin/activate
pip install --upgrade -r requirements.txt
```

### 11.2 日志查看
```bash
# 查看应用日志
tail -f /opt/ragapp/logs/ragapp-main.log
tail -f /opt/ragapp/logs/ragapp-pipeline.log

# 查看Nginx日志
tail -f /var/log/nginx/ragapp_access.log
tail -f /var/log/nginx/ragapp_error.log

# 查看系统日志
journalctl -u nginx -f
journalctl -u supervisor -f
```

### 11.3 重启服务
```bash
# 重启应用服务
sudo supervisorctl restart ragapp-main
sudo supervisorctl restart ragapp-pipeline

# 重启Nginx
sudo systemctl restart nginx

# 重启所有相关服务
sudo systemctl restart supervisor nginx
```

## 12. 维护和监控

### 12.1 定期备份
```bash
# 创建备份脚本
sudo vim /opt/ragapp/scripts/backup.sh
```

### 12.2 监控脚本
```bash
# 创建监控脚本
sudo vim /opt/ragapp/scripts/monitor.sh
```

### 12.3 日志轮转
```bash
# 配置logrotate
sudo vim /etc/logrotate.d/ragapp
```

---

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 服务器系统日志
2. 应用程序日志
3. Nginx错误日志
4. 防火墙和安全组配置

部署完成后，您的RAG聊天应用将通过以下方式访问：
- 前端界面: `http://your_domain.com`
- RAG API: `http://your_domain.com/api/`
- 数据管道API: `http://your_domain.com/data-api/`
